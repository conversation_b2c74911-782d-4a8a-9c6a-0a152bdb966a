<?php
/**
 * Team Member Details Page
 * Individual team member profile page
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Check if team details are enabled
$enable_team_details = getThemeOption('enable_team_details', 0);
if (!$enable_team_details) {
    header('Location: team.php');
    exit;
}

// Check if this is CEO profile or regular team member
$is_ceo = isset($_GET['ceo']) && $_GET['ceo'] == '1';
$member_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$is_ceo && !$member_id) {
    header('Location: team.php');
    exit;
}

if ($is_ceo) {
    // Get CEO information from theme options
    $member = [
        'name' => getThemeOption('ceo_name', '<PERSON>'),
        'position' => getThemeOption('ceo_title', 'Chief Executive Officer & Principal Architect'),
        'bio' => getThemeOption('ceo_bio', 'With over 25 years of experience in architectural design and construction management, <PERSON> founded Monolith Design with a vision to create spaces that inspire and endure.'),
        'photo' => getThemeOption('ceo_photo', ''),
        'email' => getThemeOption('ceo_email', ''),
        'linkedin_url' => getThemeOption('ceo_linkedin', ''),
        'achievements' => getThemeOption('ceo_achievements', 'Licensed Architect in 12 states\nLEED AP BD+C Certified Professional\nAIA Gold Medal Recipient (2023)'),
        'education' => getThemeOption('ceo_education', 'Master of Architecture|Massachusetts Institute of Technology (MIT)|1998\nBachelor of Architecture|University of California, Berkeley|1996\nProfessional Licenses|Licensed Architect in 12 states including CA, NY, TX, FL|Current'),
        'philosophy_quote' => getThemeOption('ceo_philosophy_quote', 'Architecture is not just about creating buildings; it\'s about crafting experiences that inspire, spaces that endure, and environments that enhance human life.'),
        'philosophy_description' => getThemeOption('ceo_philosophy_description', 'Alexander\'s leadership approach combines visionary thinking with practical execution, ensuring that every project delivered by Monolith Design exceeds client expectations.'),
        'projects' => getThemeOption('ceo_projects', 'Metropolitan Arts Center|$150M cultural complex featuring sustainable design and innovative acoustic engineering\nSkyline Corporate Headquarters|LEED Platinum certified office tower with revolutionary energy management systems\nRiverside Residential Complex|Award-winning mixed-use development integrating affordable housing with luxury amenities')
    ];
} else {
    // Get team member data
    try {
        $db = Database::getConnection();
        $stmt = $db->prepare("SELECT * FROM team_members WHERE id = ? AND active = 1");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if (!$member) {
            header('Location: team.php');
            exit;
        }
    } catch (Exception $e) {
        header('Location: team.php');
        exit;
    }
}

$pageTitle = htmlspecialchars($member['name']) . ' - ' . htmlspecialchars($member['position']);
$pageDescription = 'Learn more about ' . htmlspecialchars($member['name']) . ', ' . htmlspecialchars($member['position']) . ' at Monolith Design.';

// Get featured projects for "Featured Work" section
try {
    $featured_projects = getProjects(null, 4); // Get 4 featured projects
} catch (Exception $e) {
    $featured_projects = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php loadTemplate('head', [
        'title' => $pageTitle,
        'description' => $pageDescription
    ]); ?>
    
    <!-- Team Details Page Specific CSS -->
    <style>
        /* ===== TEAM DETAILS PAGE STYLES ===== */
        
        /* Hero Section */
        .team-detail-hero {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 100%), 
                        url('https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=1600&h=900&fit=crop') center/cover;
            padding: 8rem 0;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .team-detail-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--accent-color);
            opacity: 0.1;
            z-index: 1;
        }
        
        .team-detail-hero .container {
            position: relative;
            z-index: 2;
        }
        
        .team-detail-hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .team-detail-hero .position {
            font-size: 1.5rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .breadcrumb {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .breadcrumb a:hover {
            color: white;
        }
        
        .breadcrumb span {
            color: rgba(255, 255, 255, 0.6);
        }
        
        /* Main Content */
        .team-detail-content {
            padding: 8rem 0;
        }
        
        .team-profile {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4rem;
            align-items: start;
            margin-bottom: 6rem;
        }
        
        .team-profile-image {
            position: relative;
        }
        
        .team-profile-image img {
            width: 100%;
            max-width: 400px;
            aspect-ratio: 4/5;
            object-fit: cover;
            border-radius: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .team-profile-image::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: -20px;
            bottom: -20px;
            border: 3px solid var(--accent-color);
            z-index: -1;
        }
        
        .team-profile-info h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .team-role {
            font-size: 1.3rem;
            color: var(--accent-color);
            font-weight: 600;
            margin-bottom: 2rem;
        }
        
        .team-bio {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 2rem;
        }
        
        .team-contact {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: #f8f9fa;
            border-radius: 50px;
            text-decoration: none;
            color: var(--text-color);
            transition: all 0.3s;
        }
        
        .contact-item:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .team-expertise {
            background: #f8f9fa;
            padding: 3rem;
            border-radius: 10px;
            margin-bottom: 4rem;
        }
        
        .team-expertise h3 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: var(--text-color);
        }
        
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .expertise-item {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        
        .expertise-item:hover {
            transform: translateY(-5px);
        }
        
        .expertise-item h4 {
            color: var(--accent-color);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        /* CEO Achievements */
        .ceo-achievements-detail {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .achievement-item {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            font-weight: 500;
            color: var(--text-color);
            transition: transform 0.3s;
        }

        .achievement-item:hover {
            transform: translateY(-3px);
        }

        /* CEO Additional Sections */
        .ceo-additional-sections {
            margin-top: 2rem;
        }

        .ceo-section {
            margin-bottom: 3rem;
        }

        .ceo-section h3 {
            color: var(--accent-color);
            font-size: 2rem;
            margin-bottom: 2rem;
            padding-bottom: 0.5rem;
        }

        .education-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .education-item {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .education-item:hover {
            transform: translateY(-5px);
        }

        .education-item h4 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
        }

        .education-item p {
            color: var(--text-color);
            margin-bottom: 1rem;
        }

        .education-item .year {
            background: var(--accent-color);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .philosophy-content blockquote {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            border-radius: 10px;
            font-style: italic;
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: var(--text-color);
        }

        .philosophy-content p {
            font-size: 1.1rem;
            line-height: 1.7;
            color: var(--text-color);
        }

        .projects-highlight {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .project-item {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }

        .project-item:hover {
            transform: translateY(-5px);
        }

        .project-item h4 {
            color: var(--accent-color);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .project-item p {
            color: var(--text-color);
            line-height: 1.6;
        }

        /* Featured Work Section - Match Home Page Design */
        .featured-projects-section {
            padding: 4rem 0;
        }

        .featured-projects-section .section-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .featured-projects-section .subtitle {
            color: var(--accent-color);
            font-weight: 600;
            font-size: 0.9rem;
            letter-spacing: 2px;
            margin-bottom: 1rem;
        }

        .featured-projects-section h3 {
            font-size: 2.5rem;
            color: var(--text-color);
            margin: 0;
        }

        .work-outer-main {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .work-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .work-main-card {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
        }

        .work-main {
            aspect-ratio: 16/10;
            overflow: hidden;
            position: relative;
            background-color: #f5f5f5;
            height: 400px;
            width: 100%;
            border-radius: 15px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            transition: transform 0.3s;
        }

        .work-main:hover {
            transform: scale(1.02);
        }

        .work-main img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .work-main-card:hover .work-main img {
            transform: scale(1.1);
        }

        .work-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 2rem;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
        }

        .work-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .work-mini-card {
            background-color: rgba(255,255,255,0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            font-size: 0.8rem;
            font-weight: 500;
        }

        .work-content h6 {
            font-size: 1.5rem;
            margin: 0;
            font-weight: 600;
        }

        .work-list {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .work-card {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 1rem;
            padding: 1rem;
            border-radius: 10px;
            transition: all 0.3s;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.5);
            position: relative;
        }

        .work-card:hover {
            background-color: var(--secondary-color, #f8f9fa);
            transform: translateY(-2px);
        }

        .work-cover {
            aspect-ratio: 4/3;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            background-color: #f5f5f5;
            height: 180px;
            width: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .work-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .work-card:hover .work-cover img {
            transform: scale(1.1);
        }

        .work-link {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
        }
        
        /* Back Navigation */
        .back-navigation {
            margin-bottom: 3rem;
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .back-btn:hover {
            color: var(--text-color);
            transform: translateX(-5px);
        }
        
        /* Responsive Design */
        @media (max-width: 992px) {
            .team-profile {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }
            
            .team-detail-hero h1 {
                font-size: 2.5rem;
            }
            
            .team-contact {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .work-wrapper {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .work-card {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
        
        @media (max-width: 768px) {
            .team-detail-hero,
            .team-detail-content,
            .other-team-members {
                padding: 4rem 0;
            }
            
            .team-detail-hero h1 {
                font-size: 2rem;
            }
            
            .expertise-grid {
                grid-template-columns: 1fr;
            }
            
            .featured-projects-section {
                padding: 3rem 0;
            }
            
            .work-card {
                padding: 0.75rem;
            }
            
            .work-cover {
                height: 150px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Team Detail Hero Section -->
    <section class="team-detail-hero">
        <div class="container">
            <div class="breadcrumb">
                <a href="<?php echo siteUrl(); ?>">Home</a>
                <span>›</span>
                <a href="<?php echo siteUrl('team'); ?>">Team</a>
                <span>›</span>
                <span><?php echo htmlspecialchars($member['name']); ?></span>
            </div>
            <h1><?php echo htmlspecialchars($member['name']); ?></h1>
            <div class="position"><?php echo htmlspecialchars($member['position']); ?></div>
        </div>
    </section>

    <!-- Team Detail Content -->
    <section class="team-detail-content">
        <div class="container">
            <div class="back-navigation">
                <a href="<?php echo siteUrl('team'); ?>" class="back-btn">
                    ← Back to Team
                </a>
            </div>
            
            <div class="team-profile">
                <div class="team-profile-image">
                    <?php if ($member['photo']): ?>
                        <img src="<?php echo ensureAbsoluteUrl($member['photo']); ?>" alt="<?php echo htmlspecialchars($member['name']); ?>">
                    <?php else: ?>
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=800&fit=crop&crop=face" alt="<?php echo htmlspecialchars($member['name']); ?>">
                    <?php endif; ?>
                </div>
                
                <div class="team-profile-info">
                    <h2><?php echo htmlspecialchars($member['name']); ?></h2>
                    <div class="team-role"><?php echo htmlspecialchars($member['position']); ?></div>
                    
                    <?php if ($member['bio']): ?>
                        <div class="team-bio">
                            <?php
                            if ($is_ceo) {
                                // For CEO, show full comprehensive bio with paragraphs
                                $bio_paragraphs = explode('\n\n', $member['bio']);
                                foreach ($bio_paragraphs as $paragraph) {
                                    if (trim($paragraph)) {
                                        echo '<p>' . nl2br(htmlspecialchars(trim($paragraph))) . '</p>';
                                    }
                                }
                            } else {
                                // For regular team members, show standard bio
                                echo nl2br(htmlspecialchars($member['bio']));
                            }
                            ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="team-contact">
                        <?php if ($member['email']): ?>
                            <a href="mailto:<?php echo htmlspecialchars($member['email']); ?>" class="contact-item">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                </svg>
                                Email
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($member['linkedin_url']): ?>
                            <a href="<?php echo htmlspecialchars($member['linkedin_url']); ?>" target="_blank" class="contact-item">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                LinkedIn
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Expertise/Achievements Section -->
            <div class="team-expertise">
                <?php if ($is_ceo): ?>
                    <h3>Achievements & Recognition</h3>
                    <div class="ceo-achievements-detail">
                        <?php
                        $achievements = explode('\n', $member['achievements']);
                        foreach ($achievements as $achievement) {
                            if (trim($achievement)) {
                                echo '<div class="achievement-item">✓ ' . htmlspecialchars(trim($achievement)) . '</div>';
                            }
                        }
                        ?>
                    </div>

                    <!-- CEO Additional Sections -->
                    <div class="ceo-additional-sections">
                        <div class="ceo-section">
                            <h3>Education & Credentials</h3>
                            <div class="education-grid">
                                <?php
                                $education_items = explode('\n', $member['education']);
                                foreach ($education_items as $education) {
                                    if (trim($education)) {
                                        $parts = explode('|', trim($education));
                                        if (count($parts) >= 3) {
                                            $degree = trim($parts[0]);
                                            $institution = trim($parts[1]);
                                            $year = trim($parts[2]);
                                            echo '<div class="education-item">';
                                            echo '<h4>' . htmlspecialchars($degree) . '</h4>';
                                            echo '<p>' . htmlspecialchars($institution) . '</p>';
                                            echo '<span class="year">' . htmlspecialchars($year) . '</span>';
                                            echo '</div>';
                                        }
                                    }
                                }
                                ?>
                            </div>
                        </div>

                        <div class="ceo-section">
                            <h3>Leadership Philosophy</h3>
                            <div class="philosophy-content">
                                <blockquote>
                                    "<?php echo htmlspecialchars($member['philosophy_quote']); ?>"
                                </blockquote>
                                <p><?php echo htmlspecialchars($member['philosophy_description']); ?></p>
                            </div>
                        </div>

                        <div class="ceo-section">
                            <h3>Notable Projects</h3>
                            <div class="projects-highlight">
                                <?php
                                $project_items = explode('\n', $member['projects']);
                                foreach ($project_items as $project) {
                                    if (trim($project)) {
                                        $parts = explode('|', trim($project));
                                        if (count($parts) >= 2) {
                                            $project_name = trim($parts[0]);
                                            $project_description = trim($parts[1]);
                                            echo '<div class="project-item">';
                                            echo '<h4>' . htmlspecialchars($project_name) . '</h4>';
                                            echo '<p>' . htmlspecialchars($project_description) . '</p>';
                                            echo '</div>';
                                        }
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <h3>Areas of Expertise</h3>
                    <div class="expertise-grid">
                        <div class="expertise-item">
                            <h4>Project Management</h4>
                            <p>Leading complex architectural projects from conception to completion with exceptional attention to detail and timeline management.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Sustainable Design</h4>
                            <p>Implementing eco-friendly design principles and LEED certification standards in residential and commercial projects.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Client Relations</h4>
                            <p>Building strong relationships with clients through clear communication and understanding of their unique vision and requirements.</p>
                        </div>
                        <div class="expertise-item">
                            <h4>Technical Innovation</h4>
                            <p>Utilizing cutting-edge design software and construction technologies to deliver innovative architectural solutions.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Featured Work Section - Arkify Style -->
    <section class="featured-projects-section">
        <div class="container">
            <div class="section-title">
                <div class="subtitle">
                    <div>PROJECT</div>
                </div>
                <h2>Featured Work</h2>
            </div>

            <div class="work-outer-main">
                <div class="work-wrapper">
                    <?php if (false && !empty($featured_projects)): ?>
                        <!-- Main featured project -->
                        <div class="work-main-card">
                            <div class="work-main">
                                <img src="<?php echo $featured_projects[0]['featured_image']; ?>" alt="<?php echo htmlspecialchars($featured_projects[0]['title']); ?>" class="cover-image">
                            </div>
                            <div class="work-content">
                                <div class="work-title">
                                    <div><?php echo htmlspecialchars($featured_projects[0]['location'] ?? 'Location'); ?></div>
                                    <div class="card-outer">
                                        <div class="work-mini-card">
                                            <div class="body-small"><?php echo htmlspecialchars($featured_projects[0]['category']); ?></div>
                                        </div>
                                    </div>
                                </div>
                                <h6><?php echo htmlspecialchars($featured_projects[0]['title']); ?></h6>
                            </div>
                            <a href="<?php echo siteUrl('project/' . $featured_projects[0]['slug']); ?>" class="work-link"></a>
                        </div>

                        <!-- Secondary projects -->
                        <div class="work-list">
                            <?php for ($i = 1; $i < min(4, count($featured_projects)); $i++): ?>
                                <div class="work-card">
                                    <div class="work-cover">
                                        <img src="<?php echo $featured_projects[$i]['featured_image']; ?>" alt="<?php echo htmlspecialchars($featured_projects[$i]['title']); ?>" class="cover-image">
                                    </div>
                                    <div class="work-content">
                                        <div class="work-title">
                                            <div><?php echo htmlspecialchars($featured_projects[$i]['location'] ?? 'Location'); ?></div>
                                            <div class="work-mini-card">
                                                <div class="body-small"><?php echo htmlspecialchars($featured_projects[$i]['category']); ?></div>
                                            </div>
                                        </div>
                                        <h6><?php echo htmlspecialchars($featured_projects[$i]['title']); ?></h6>
                                    </div>
                                    <a href="<?php echo siteUrl('project/' . $featured_projects[$i]['slug']); ?>" class="work-link"></a>
                                </div>
                            <?php endfor; ?>
                        </div>
                    <?php else: ?>
                        <!-- Default projects with background images -->
                        <div class="work-main-card">
                            <div class="work-main" style="background-image: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=building');">
                            </div>
                            <div class="work-content">
                                <div class="work-title">
                                    <div>San Francisco, CA</div>
                                    <div class="card-outer">
                                        <div class="work-mini-card">
                                            <div class="body-small">Commercial</div>
                                        </div>
                                    </div>
                                </div>
                                <h6>Modern Office Complex</h6>
                            </div>
                        </div>

                        <!-- Secondary projects -->
                        <div class="work-list">
                            <div class="work-card">
                                <div class="work-cover" style="background-image: url('https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400&h=300&fit=crop&crop=building');">
                                </div>
                                <div class="work-content">
                                    <div class="work-title">
                                        <div>New York, NY</div>
                                        <div class="work-mini-card">
                                            <div class="body-small">Residential</div>
                                        </div>
                                    </div>
                                    <h6>Luxury Residential Tower</h6>
                                </div>
                            </div>

                            <div class="work-card">
                                <div class="work-cover" style="background-image: url('https://images.unsplash.com/photo-1448630360428-65456885c650?w=400&h=300&fit=crop&crop=building');">
                                </div>
                                <div class="work-content">
                                    <div class="work-title">
                                        <div>Austin, TX</div>
                                        <div class="work-mini-card">
                                            <div class="body-small">Commercial</div>
                                        </div>
                                    </div>
                                    <h6>Innovation Center</h6>
                                </div>
                            </div>

                            <div class="work-card">
                                <div class="work-cover" style="background-image: url('https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=400&h=300&fit=crop&crop=building');">
                                </div>
                                <div class="work-content">
                                    <div class="work-title">
                                        <div>Portland, OR</div>
                                        <div class="work-mini-card">
                                            <div class="body-small">Residential</div>
                                        </div>
                                    </div>
                                    <h6>Sustainable Housing Development</h6>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="work-last">
                    <p>Our strategic and technical advisory service isn't based on a hunch. It's backed by years of experience, extensive technical knowledge and data-driven insights. With our trusted advice,</p>
                    <a href="<?php echo siteUrl('projects'); ?>" class="primary-button">
                        <div>VIEW ALL</div>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Hero CTA Section -->
    <?php include 'templates/hero-cta.php'; ?>

    <!-- Footer -->
    <?php loadFooter(); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>
</body>
</html>
