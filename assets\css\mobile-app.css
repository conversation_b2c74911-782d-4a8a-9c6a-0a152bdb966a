/**
 * Mobile App-like Design System
 * Native mobile app experience for Monolith Design
 */

/* Mobile App Variables */
:root {
    /* Mobile App Colors */
    --app-primary: #1A1A1A;
    --app-secondary: #F5F5F5;
    --app-accent: #E67E22;
    --app-white: #FFFFFF;
    --app-gray-light: #F8F9FA;
    --app-gray-medium: #E5E5E5;
    --app-gray-dark: #666666;
    --app-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    --app-shadow-heavy: 0 4px 20px rgba(0, 0, 0, 0.15);
    
    /* Mobile App Spacing */
    --app-spacing-xs: 4px;
    --app-spacing-sm: 8px;
    --app-spacing-md: 16px;
    --app-spacing-lg: 24px;
    --app-spacing-xl: 32px;
    
    /* Mobile App Typography */
    --app-font-size-xs: 12px;
    --app-font-size-sm: 14px;
    --app-font-size-md: 16px;
    --app-font-size-lg: 18px;
    --app-font-size-xl: 20px;
    
    /* Mobile App Layout */
    --app-header-height: 60px;
    --app-footer-height: 70px;
    --app-border-radius: 12px;
    --app-border-radius-sm: 8px;
    --app-border-radius-lg: 16px;
    
    /* Touch Targets */
    --app-touch-target: 44px;
    --app-touch-target-lg: 56px;
}

/* Mobile App Header */
@media (max-width: 768px) {
    .arkify-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: var(--app-white);
        box-shadow: var(--app-shadow);
        height: var(--app-header-height);
        border-bottom: 1px solid var(--app-gray-medium);
    }
    
    .header-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        padding: 0 var(--app-spacing-md);
    }
    
    .header-logo .main-logo {
        height: 32px;
        width: auto;
    }
    
    /* Mobile Menu Toggle - App Style */
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: var(--app-touch-target);
        height: var(--app-touch-target);
        background: transparent;
        border: none;
        border-radius: var(--app-border-radius-sm);
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
    }
    
    .mobile-menu-toggle:hover {
        background: var(--app-gray-light);
    }
    
    .mobile-menu-toggle:active {
        transform: scale(0.95);
        background: var(--app-gray-medium);
    }
    
    /* Hamburger Lines - App Style */
    .hamburger-line {
        display: block;
        width: 20px;
        height: 2px;
        background: var(--app-primary);
        border-radius: 1px;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: absolute;
    }
    
    .hamburger-line:nth-child(1) {
        top: 16px;
    }
    
    .hamburger-line:nth-child(2) {
        top: 22px;
    }
    
    .hamburger-line:nth-child(3) {
        top: 28px;
    }
    
    /* Active State Animation */
    .mobile-menu-toggle.active .hamburger-line:nth-child(1) {
        transform: rotate(45deg) translate(4px, 4px);
    }
    
    .mobile-menu-toggle.active .hamburger-line:nth-child(2) {
        opacity: 0;
    }
    
    .mobile-menu-toggle.active .hamburger-line:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }
    
    /* Mobile Navigation - App Style */
    .mobile-navigation {
        position: fixed;
        top: var(--app-header-height);
        left: 0;
        right: 0;
        bottom: var(--app-footer-height);
        background: var(--app-white);
        z-index: 999;
        overflow-y: auto;
        transform: translateX(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        display: block !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .mobile-navigation.active {
        transform: translateX(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    .mobile-nav-content {
        padding: var(--app-spacing-lg) var(--app-spacing-md);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .mobile-nav-menu {
        width: 100%;
        max-width: 280px;
        display: flex;
        flex-direction: column;
        gap: var(--app-spacing-xs);
    }
    
    /* Mobile Navigation Links - App Style */
    .mobile-nav-link {
        display: flex;
        align-items: center;
        padding: var(--app-spacing-md) var(--app-spacing-lg);
        margin-bottom: var(--app-spacing-sm);
        background: var(--app-gray-light);
        border-radius: var(--app-border-radius);
        color: var(--app-primary);
        text-decoration: none;
        font-size: var(--app-font-size-md);
        font-weight: 500;
        min-height: var(--app-touch-target-lg);
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }
    
    .mobile-nav-link:hover,
    .mobile-nav-link:active {
        background: var(--app-accent);
        color: var(--app-white);
        transform: translateY(-1px);
        box-shadow: var(--app-shadow);
    }
    
    .mobile-nav-link::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: var(--app-accent);
        transform: scaleY(0);
        transition: transform 0.2s ease;
    }
    
    .mobile-nav-link:hover::before {
        transform: scaleY(1);
    }
    
    /* Mobile CTA Button - App Style */
    .mobile-cta-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--app-spacing-sm);
        padding: var(--app-spacing-md) var(--app-spacing-lg);
        margin-top: var(--app-spacing-xl);
        background: var(--app-accent);
        color: var(--app-white);
        border-radius: var(--app-border-radius);
        text-decoration: none;
        font-size: var(--app-font-size-md);
        font-weight: 600;
        min-height: var(--app-touch-target-lg);
        box-shadow: var(--app-shadow);
        transition: all 0.2s ease;
    }
    
    .mobile-cta-button:hover,
    .mobile-cta-button:active {
        background: #d35400;
        transform: translateY(-2px);
        box-shadow: var(--app-shadow-heavy);
    }
    
    /* Body Padding for Fixed Header */
    body {
        padding-top: var(--app-header-height);
    }
    
    /* Hide Desktop Navigation */
    .main-navigation,
    .header-cta-button {
        display: none !important;
    }

    /* Show Mobile Menu Toggle */
    .mobile-menu-toggle {
        display: flex !important;
    }
}

/* Hide Mobile Tab Bar on Desktop */
.mobile-tab-bar {
    display: none !important;
}

/* Mobile App Footer */
@media (max-width: 768px) {
    .modern-footer,
    .footer-component {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: var(--app-white);
        border-top: 1px solid var(--app-gray-medium);
        box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
        height: var(--app-footer-height);
        padding: 0;
    }

    .footer-content {
        display: none !important;
    }

    /* Mobile Tab Bar Navigation - Show only on mobile */
    .mobile-tab-bar {
        display: flex !important;
        align-items: center;
        justify-content: space-around;
        height: 100%;
        padding: var(--app-spacing-sm) 0;
    }
    
    .mobile-tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2px;
        padding: var(--app-spacing-xs);
        color: var(--app-gray-dark);
        text-decoration: none;
        font-size: var(--app-font-size-xs);
        font-weight: 500;
        min-width: var(--app-touch-target);
        min-height: var(--app-touch-target);
        border-radius: var(--app-border-radius-sm);
        transition: all 0.2s ease;
        position: relative;
    }
    
    .mobile-tab-item.active {
        color: var(--app-accent);
        background: rgba(230, 126, 34, 0.1);
    }
    
    .mobile-tab-item:hover {
        color: var(--app-accent);
        background: rgba(230, 126, 34, 0.05);
    }
    
    .mobile-tab-icon {
        width: 20px;
        height: 20px;
        stroke: currentColor;
        fill: none;
        stroke-width: 2;
        stroke-linecap: round;
        stroke-linejoin: round;
    }
    
    .mobile-tab-label {
        font-size: 10px;
        line-height: 1;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    /* Body Padding for Fixed Footer */
    body {
        padding-bottom: var(--app-footer-height) !important;
        margin-bottom: 0 !important;
        overflow-x: hidden !important;
    }

    /* Prevent content overflow */
    * {
        box-sizing: border-box;
    }

    /* Ensure main content doesn't overlap footer */
    main {
        margin-bottom: var(--app-spacing-md) !important;
    }

    /* Fix sections that might cause overflow */
    .hero-cta-section,
    .cta-section,
    .footer-component {
        margin-bottom: var(--app-spacing-md) !important;
    }

    /* Prevent horizontal scroll on containers */
    .container {
        max-width: 100% !important;
        overflow-x: hidden !important;
    }
}

/* Desktop Footer Styles - Ensure normal footer behavior on desktop */
@media (min-width: 769px) {
    .modern-footer,
    .footer-component {
        position: static !important;
        height: auto !important;
        padding: 4rem 0 2rem 0 !important;
        background: #1A1A1A !important;
        border-top: none !important;
        box-shadow: none !important;
    }

    .footer-content {
        display: block !important;
    }

    .mobile-tab-bar {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }

    body {
        padding-bottom: 0 !important;
        padding-top: 0 !important;
    }
}

/* Mobile App Content Styling */
@media (max-width: 768px) {
    /* Section Padding - App Style */
    .section-padding {
        padding: var(--app-spacing-xl) 0;
    }

    /* Container Padding */
    .container {
        padding: 0 var(--app-spacing-md);
    }

    /* Typography - App Style */
    h1 {
        font-size: 1.75rem;
        line-height: 1.2;
        margin-bottom: var(--app-spacing-md);
    }

    h2 {
        font-size: 1.5rem;
        line-height: 1.3;
        margin-bottom: var(--app-spacing-md);
    }

    h3 {
        font-size: 1.25rem;
        line-height: 1.3;
        margin-bottom: var(--app-spacing-sm);
    }

    /* Buttons - App Style */
    .btn,
    .primary-button,
    .secondary-button {
        min-height: var(--app-touch-target);
        padding: var(--app-spacing-sm) var(--app-spacing-lg);
        border-radius: var(--app-border-radius);
        font-size: var(--app-font-size-md);
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--app-spacing-sm);
        transition: all 0.2s ease;
        box-shadow: var(--app-shadow);
        border: none;
        cursor: pointer;
    }

    .btn:hover,
    .primary-button:hover,
    .secondary-button:hover {
        transform: translateY(-1px);
        box-shadow: var(--app-shadow-heavy);
    }

    .btn:active,
    .primary-button:active,
    .secondary-button:active {
        transform: translateY(0);
    }

    /* Cards - App Style */
    .service-card,
    .project-card,
    .team-member,
    .work-card {
        border-radius: var(--app-border-radius);
        box-shadow: var(--app-shadow);
        transition: all 0.2s ease;
        overflow: hidden;
        background: var(--app-white);
        border: 1px solid var(--app-gray-medium);
    }

    .service-card:hover,
    .project-card:hover,
    .team-member:hover,
    .work-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--app-shadow-heavy);
    }

    /* Hero Sections - App Style */
    .hero-section,
    .page-hero {
        border-radius: 0 0 var(--app-border-radius-lg) var(--app-border-radius-lg);
        overflow: hidden;
    }

    /* Form Elements - App Style */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="url"],
    textarea,
    select {
        min-height: var(--app-touch-target);
        padding: var(--app-spacing-sm) var(--app-spacing-md);
        border: 2px solid var(--app-gray-medium);
        border-radius: var(--app-border-radius);
        font-size: var(--app-font-size-md);
        background: var(--app-white);
        transition: all 0.2s ease;
    }

    input:focus,
    textarea:focus,
    select:focus {
        outline: none;
        border-color: var(--app-accent);
        box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
    }

    /* Navigation Links - App Style */
    .nav-link {
        min-height: var(--app-touch-target);
        padding: var(--app-spacing-sm) var(--app-spacing-md);
        border-radius: var(--app-border-radius-sm);
        transition: all 0.2s ease;
    }

    /* Grid Layouts - App Style */
    .services-grid,
    .projects-grid,
    .team-members {
        gap: var(--app-spacing-md);
    }

    /* Spacing Utilities */
    .mb-app-xs { margin-bottom: var(--app-spacing-xs); }
    .mb-app-sm { margin-bottom: var(--app-spacing-sm); }
    .mb-app-md { margin-bottom: var(--app-spacing-md); }
    .mb-app-lg { margin-bottom: var(--app-spacing-lg); }
    .mb-app-xl { margin-bottom: var(--app-spacing-xl); }

    .mt-app-xs { margin-top: var(--app-spacing-xs); }
    .mt-app-sm { margin-top: var(--app-spacing-sm); }
    .mt-app-md { margin-top: var(--app-spacing-md); }
    .mt-app-lg { margin-top: var(--app-spacing-lg); }
    .mt-app-xl { margin-top: var(--app-spacing-xl); }

    .p-app-xs { padding: var(--app-spacing-xs); }
    .p-app-sm { padding: var(--app-spacing-sm); }
    .p-app-md { padding: var(--app-spacing-md); }
    .p-app-lg { padding: var(--app-spacing-lg); }
    .p-app-xl { padding: var(--app-spacing-xl); }

    /* App-like Animations */
    .app-fade-in {
        animation: appFadeIn 0.3s ease-out;
    }

    .app-slide-up {
        animation: appSlideUp 0.3s ease-out;
    }

    .app-bounce {
        animation: appBounce 0.5s ease-out;
    }
}

/* App Animations */
@keyframes appFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes appSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes appBounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Mobile App Status Bar Spacing */
@media (max-width: 768px) {
    /* iOS Safari status bar */
    @supports (padding-top: env(safe-area-inset-top)) {
        .arkify-header {
            padding-top: env(safe-area-inset-top);
            height: calc(var(--app-header-height) + env(safe-area-inset-top));
        }

        body {
            padding-top: calc(var(--app-header-height) + env(safe-area-inset-top));
        }
    }

    /* Android Chrome address bar */
    @supports (height: 100dvh) {
        .mobile-navigation {
            height: 100dvh;
        }
    }
}
